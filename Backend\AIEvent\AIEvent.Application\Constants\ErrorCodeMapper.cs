﻿using AIEvent.Application.DTO.Common;
using System.Net;

namespace AIEvent.Application.Constants
{
    public static class ErrorCodeMapper
    {
        private static readonly Dictionary<HttpStatusCode, (string ErrorCode, string Message)> ErrorMappings = new()
        {
            { HttpStatusCode.BadRequest, (ErrorCodes.InvalidInput, ErrorMessages.BadRequest) },
            { HttpStatusCode.Unauthorized, (ErrorCodes.TokenInvalid, ErrorMessages.Unauthorized) },
            { HttpStatusCode.Forbidden, (ErrorCodes.PermissionDenied, ErrorMessages.Forbidden) },
            { HttpStatusCode.NotFound, (ErrorCodes.NotFound, ErrorMessages.NotFound) },
            { HttpStatusCode.InternalServerError, (ErrorCodes.InternalServerError, ErrorMessages.InternalServerError) },
        };

        public static ErrorResponse GetError(HttpStatusCode statusCode)
        {
            var (errorCode, message) = ErrorMappings.TryGetValue(statusCode, out var value)
                ? value
                : (ErrorCodes.InternalServerError, ErrorMessages.InternalServerError);

            return new ErrorResponse
            {
                Success = false,
                StatusCode = errorCode,
                Message = message,
            };
        }
    }
}
