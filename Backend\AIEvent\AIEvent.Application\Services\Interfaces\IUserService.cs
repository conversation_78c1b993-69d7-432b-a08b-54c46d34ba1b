using AIEvent.Application.DTO.Auth;
using AIEvent.Application.DTO.Common;
using AIEvent.Application.DTO.User;

namespace AIEvent.Application.Services.Interfaces
{
    public interface IUserService
    {
        Task<UserResponse> GetUserByIdAsync(string userId);
        Task<UserResponse> UpdateUserAsync(string userId, UpdateUserRequest request); 
        Task<List<UserResponse>> GetAllUsersAsync(int page = 1, int pageSize = 10);
    }
}
