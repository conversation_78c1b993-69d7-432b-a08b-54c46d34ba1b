
using AIEvent.API.Extensions;
using AIEvent.Application.Constants;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json.Serialization;

namespace AIEvent.API
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddControllers()
                            .AddJsonOptions(options =>
                                            options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter()))
                            .ConfigureApiBehaviorOptions(options =>
                            {
                                options.InvalidModelStateResponseFactory = context =>
                                {
                                    var result = new ObjectResult(new
                                    {
                                        StatusCode = ErrorCodes.InvalidInput,
                                        Message = ErrorMessages.InvalidInput
                                    })
                                    {
                                        StatusCode = StatusCodes.Status400BadRequest
                                    };
                                    return result;
                                };
                            });

            builder.Services.AddApplicationServices();
            builder.Services.AddInfrastructureServices(builder.Configuration);
            builder.Services.AddJwtAuthentication(builder.Configuration);

            builder.Services.AddCustomCors(builder.Configuration);

            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            builder.Services.AddEndpointsApiExplorer();

            builder.Services.AddSwaggerCustoms();
            builder.Services.AddHttpContextAccessor();


            var app = builder.Build();

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI();
            }

            app.UseHttpsRedirection();

            app.UseCors();

            app.UseAuthentication();
            app.UseAuthorization();

            app.MapControllers();

            app.Run();
        }
    }
}
