﻿using AIEvent.Application.Constants;

namespace AIEvent.Application.DTO.Common
{
    public class SuccessReponse<T> : BaseResponse 
    {
        public T? Data { get; set; }

        public static SuccessReponse<T> SuccessResult(T data, string statusCode = SuccessCodes.Success, string message = SuccessMessages.Success)
        {
            return new SuccessReponse<T>
            {
                Success = true,
                StatusCode = statusCode,
                Message = message,
                Data = data
            };
        }
    }
}
