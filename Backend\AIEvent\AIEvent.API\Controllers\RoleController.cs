using AIEvent.Application.DTO.Common;
using AIEvent.Application.DTO.Role;
using AIEvent.Application.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AIEvent.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]
    public class RoleController : ControllerBase
    {
        private readonly IRoleService _roleService;

        public RoleController(IRoleService roleService)
        {
            _roleService = roleService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllRoles()
        {
            try
            {
                var result = await _roleService.GetAllRolesAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all roles");
                return StatusCode(500, new BaseResponse<List<RoleResponse>>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Get role by ID
        /// </summary>
        /// <param name="id">Role ID</param>
        /// <returns>Role information</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<BaseResponse<RoleResponse>>> GetRole(string id)
        {
            try
            {
                var result = await _roleService.GetRoleByIdAsync(id);
                
                if (!result.Success)
                {
                    return NotFound(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting role: {RoleId}", id);
                return StatusCode(500, new BaseResponse<RoleResponse>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Get role by name
        /// </summary>
        /// <param name="name">Role name</param>
        /// <returns>Role information</returns>
        [HttpGet("by-name/{name}")]
        public async Task<ActionResult<BaseResponse<RoleResponse>>> GetRoleByName(string name)
        {
            try
            {
                var result = await _roleService.GetRoleByNameAsync(name);
                
                if (!result.Success)
                {
                    return NotFound(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting role by name: {RoleName}", name);
                return StatusCode(500, new BaseResponse<RoleResponse>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Create new role
        /// </summary>
        /// <param name="request">Create role request</param>
        /// <returns>Created role information</returns>
        [HttpPost]
        public async Task<ActionResult<BaseResponse<RoleResponse>>> CreateRole([FromBody] CreateRoleRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new BaseResponse<RoleResponse>
                    {
                        Success = false,
                        Message = "Invalid input data",
                        Errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()
                    });
                }

                var result = await _roleService.CreateRoleAsync(request);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return CreatedAtAction(nameof(GetRole), new { id = result.Data!.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating role: {RoleName}", request.Name);
                return StatusCode(500, new BaseResponse<RoleResponse>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Update role
        /// </summary>
        /// <param name="id">Role ID</param>
        /// <param name="request">Update role request</param>
        /// <returns>Updated role information</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<BaseResponse<RoleResponse>>> UpdateRole(string id, [FromBody] UpdateRoleRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new BaseResponse<RoleResponse>
                    {
                        Success = false,
                        Message = "Invalid input data",
                        Errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()
                    });
                }

                var result = await _roleService.UpdateRoleAsync(id, request);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating role: {RoleId}", id);
                return StatusCode(500, new BaseResponse<RoleResponse>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Delete role
        /// </summary>
        /// <param name="id">Role ID</param>
        /// <returns>Success response</returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult<BaseResponse<bool>>> DeleteRole(string id)
        {
            try
            {
                var result = await _roleService.DeleteRoleAsync(id);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting role: {RoleId}", id);
                return StatusCode(500, new BaseResponse<bool>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Get users in role
        /// </summary>
        /// <param name="name">Role name</param>
        /// <returns>List of user emails in the role</returns>
        [HttpGet("{name}/users")]
        public async Task<ActionResult<BaseResponse<List<string>>>> GetUsersInRole(string name)
        {
            try
            {
                var result = await _roleService.GetUsersInRoleAsync(name);
                
                if (!result.Success)
                {
                    return NotFound(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users in role: {RoleName}", name);
                return StatusCode(500, new BaseResponse<List<string>>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }
    }
}
