﻿using AIEvent.Application.Constants;

namespace AIEvent.Application.DTO.Common
{
    public class ErrorResponse : BaseResponse
    {
        public List<string> Errors { get; set; } = [];
        public static ErrorResponse FailureResult(string message, List<string>? errors = null, string statusCode = ErrorCodes.NotFound)
        {
            return new ErrorResponse
            {
                Success = false,
                StatusCode = statusCode,
                Message = message,
                Errors = errors ?? []
            };
        }
    }
}
