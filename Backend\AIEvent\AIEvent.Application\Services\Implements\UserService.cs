using AIEvent.Application.Constants;
using AIEvent.Application.DTO.Auth;
using AIEvent.Application.DTO.User;
using AIEvent.Application.Services.Interfaces;
using AIEvent.Domain.Identity;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AIEvent.Application.Services.Implements
{
    public class UserService : IUserService
    {
        private readonly UserManager<AppUser> _userManager;
        private readonly RoleManager<AppRole> _roleManager;

        public UserService(
            UserManager<AppUser> userManager,
            RoleManager<AppRole> roleManager)
        {
            _userManager = userManager;
            _roleManager = roleManager;
        }

        public async Task<UserResponse> GetUserByIdAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return null;
                }

                var roles = await _userManager.GetRolesAsync(user);
                var userInfo = new UserResponse
                {
                    Id = user.Id.ToString(),
                    Email = user.Email!,
                    FullName = user.FullName ?? "",
                    PhoneNumber = user.PhoneNumber,
                    Roles = roles.ToList(),
                    EmailConfirmed = user.EmailConfirmed,
                    CreatedAt = user.CreatedAt
                };

                return userInfo;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<UserResponse> UpdateUserAsync(string userId, UpdateUserRequest request)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return null;
                }

                if (!string.IsNullOrWhiteSpace(request.FullName))
                    user.FullName = request.FullName;

                if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
                    user.PhoneNumber = request.PhoneNumber;

                user.UpdatedAt = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    return null;
                }

                var roles = await _userManager.GetRolesAsync(user);
                var userInfo = new UserResponse
                {
                    Id = user.Id.ToString(),
                    Email = user.Email!,
                    FullName = user.FullName ?? "",
                    PhoneNumber = user.PhoneNumber,
                    Roles = roles.ToList(),
                    EmailConfirmed = user.EmailConfirmed,
                    CreatedAt = user.CreatedAt
                };

                return userInfo;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<List<UserResponse>> GetAllUsersAsync(int page = 1, int pageSize = 10)
        {
            try
            {
                var users = await _userManager.Users
                    .Where(u => u.IsActive)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var userInfos = new List<UserResponse>();
                foreach (var user in users)
                {
                    var roles = await _userManager.GetRolesAsync(user);
                    userInfos.Add(new UserResponse
                    {
                        Id = user.Id.ToString(),
                        Email = user.Email!,
                        FullName = user.FullName ?? "",
                        PhoneNumber = user.PhoneNumber,
                        Roles = roles.ToList(),
                        EmailConfirmed = user.EmailConfirmed,
                        CreatedAt = user.CreatedAt
                    });
                }

                return userInfos;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

    }
}
