﻿using AIEvent.Application.Constants;
using Microsoft.AspNetCore.Mvc;
using System.Net;

namespace AIEvent.Application.Helpers
{
    public static class ErrorHelper
    {
        public static IActionResult Create(HttpStatusCode statusCode)
        {
            var response = ErrorCodeMapper.GetError(statusCode);
            var result = new
            {
                response.StatusCode,
                response.Message,
            };

            return statusCode switch
            {
                HttpStatusCode.BadRequest => new BadRequestObjectResult(result),
                HttpStatusCode.Unauthorized => new UnauthorizedObjectResult(result),
                HttpStatusCode.Forbidden => new ObjectResult(result) { StatusCode = 403 },
                HttpStatusCode.NotFound => new NotFoundObjectResult(result),
                _ => new ObjectResult(result) { StatusCode = 500 },
            };
        }
    }
}
