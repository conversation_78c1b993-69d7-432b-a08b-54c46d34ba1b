using AIEvent.Application.Constants;
using AIEvent.Application.DTO.Auth;
using AIEvent.Application.DTO.Common;
using AIEvent.Application.DTO.User;
using AIEvent.Application.Helpers;
using AIEvent.Application.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;

namespace AIEvent.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;

        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return ErrorHelper.Create(HttpStatusCode.BadRequest);
                }

                var result = await _authService.LoginAsync(request);
                
                if (!result.Success)
                {
                    return BadRequest(BaseResponse<AuthResponse>.FailureResult(result.Message));
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new BaseResponse<AuthResponse>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// User registration
        /// </summary>
        /// <param name="request">Registration data</param>
        /// <returns>Authentication response with tokens</returns>
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<ActionResult<BaseResponse<AuthResponse>>> Register([FromBody] RegisterRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList();
                    return BadRequest(BaseResponse<AuthResponse>.FailureResult(errors, "Invalid input data"));
                }

                var result = await _authService.RegisterAsync(request);

                if (!result.Success)
                {
                    return BadRequest(BaseResponse<AuthResponse>.FailureResult(result.Message));
                }

                return Ok(BaseResponse<AuthResponse>.SuccessResult(result, result.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during registration for user: {Email}", request.Email);
                return StatusCode(500, new BaseResponse<AuthResponse>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Refresh access token using refresh token
        /// </summary>
        /// <param name="request">Refresh token request</param>
        /// <returns>New authentication response with tokens</returns>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        public async Task<ActionResult<BaseResponse<AuthResponse>>> RefreshToken([FromBody] RefreshTokenRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList();
                    return BadRequest(BaseResponse<AuthResponse>.FailureResult(errors, "Invalid input data"));
                }

                var result = await _authService.RefreshTokenAsync(request.RefreshToken);

                if (!result.Success)
                {
                    return BadRequest(BaseResponse<AuthResponse>.FailureResult(result.Message));
                }

                return Ok(BaseResponse<AuthResponse>.SuccessResult(result, result.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during token refresh");
                return StatusCode(500, new BaseResponse<AuthResponse>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Revoke refresh token (logout)
        /// </summary>
        /// <param name="request">Refresh token to revoke</param>
        /// <returns>Success response</returns>
        [HttpPost("revoke-token")]
        [Authorize]
        public async Task<ActionResult<BaseResponse<object>>> RevokeToken([FromBody] RefreshTokenRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList();
                    return BadRequest(BaseResponse<object>.FailureResult(errors, "Invalid input data"));
                }

                await _authService.RevokeRefreshTokenAsync(request.RefreshToken);

                return Ok(BaseResponse<object>.SuccessResult(new { }, "Token revoked successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during token revocation");
                return StatusCode(500, new BaseResponse<object>
                {
                    Success = false,
                    Message = "An internal server error occurred",
                    Errors = ["Internal server error"]
                });
            }
        }

        /// <summary>
        /// Get current user information
        /// </summary>
        /// <returns>Current user info</returns>
        [HttpGet("me")]
        [Authorize]
        public Task<ActionResult<BaseResponse<UserResponse>>> GetCurrentUser()
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Task.FromResult<ActionResult<BaseResponse<UserResponse>>>(
                        Unauthorized(BaseResponse<UserResponse>.UnauthorizedResult("User not found")));
                }

                var userInfo = new UserResponse
                {
                    Id = userId,
                    Email = User.FindFirst(System.Security.Claims.ClaimTypes.Email)?.Value ?? "",
                    FullName = User.FindFirst("FullName")?.Value ?? "",
                    Roles = [.. User.FindAll(System.Security.Claims.ClaimTypes.Role).Select(c => c.Value)]
                };

                return Task.FromResult<ActionResult<BaseResponse<UserResponse>>>(
                    Ok(BaseResponse<UserResponse>.SuccessResult(userInfo, "User information retrieved successfully")));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting current user info");
                return Task.FromResult<ActionResult<BaseResponse<UserResponse>>>(
                    StatusCode(500, BaseResponse<UserResponse>.FailureResult("An internal server error occurred")));
            }
        }
    }
}
