﻿namespace AIEvent.Application.Constants
{
    public class ErrorMessages
    {
        public const string Error = "An error occurred during the operation";
        public const string InvalidInput = "Missing/invalid input";
        public const string NotFound = "The requested resource was not found";
        public const string BadRequest = "The request was invalid or cannot be served";
        public const string Unauthorized = "You are not authorized to perform this action";
        public const string Forbidden = "Access to this resource is forbidden";
        public const string Conflict = "The request conflicts with the current state of the resource";
        public const string ValidationFailed = "Validation failed for the provided data";
        public const string InternalServerError = "An internal server error occurred";


    }
}
