using AIEvent.Application.DTO.Common;
using AIEvent.Application.DTO.Role;

namespace AIEvent.Application.Services.Interfaces
{
    public interface IRoleService
    {
        Task<List<RoleResponse>> GetAllRolesAsync();
        Task<RoleResponse> CreateRoleAsync(CreateRoleRequest request);
        Task<RoleResponse> UpdateRoleAsync(string roleId, UpdateRoleRequest request);
        Task<bool> DeleteRoleAsync(string roleId);
    }
}
